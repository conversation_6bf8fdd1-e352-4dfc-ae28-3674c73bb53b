<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="ghost"
        class="flex size-8 cursor-pointer items-center justify-center gap-1 rounded-md bg-secondary/70 text-foreground backdrop-blur-lg hover:bg-secondary/80"
      >
        <Wrench class="size-4" />
        <span v-if="enabledToolsCount > 0" class="text-xs">{{ enabledToolsCount }}</span>
      </Button>
    </PopoverTrigger>
    
    <PopoverContent class="w-80" align="start">
      <div class="space-y-4">
        <div>
          <h4 class="font-medium text-sm">AI Tools</h4>
          <p class="text-xs text-muted-foreground">
            Enable tools to enhance the AI's capabilities
          </p>
        </div>
        
        <div class="space-y-3">
          <div
            v-for="tool in availableTools"
            :key="tool.id"
            class="flex items-center justify-between"
          >
            <div class="flex items-center gap-3">
              <component :is="tool.icon" class="h-4 w-4" />
              <div>
                <div class="font-medium text-sm">{{ tool.name }}</div>
                <div class="text-xs text-muted-foreground">{{ tool.description }}</div>
              </div>
            </div>
            
            <Switch
              :checked="isToolEnabled(tool.id)"
              @update:checked="toggleTool(tool.id)"
              :disabled="!isToolSupported(tool.id)"
            />
          </div>
        </div>
        
        <div v-if="hasUnsupportedTools" class="pt-2 border-t">
          <p class="text-xs text-muted-foreground">
            Some tools are not available for the selected model
          </p>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useModelStore, MODELS_SHARED } from '@/stores/model'
import { 
  Wrench, 
  Search, 
  Code, 
  Eye, 
  Image as ImageIcon,
  Calculator
} from 'lucide-vue-next'

// Import UI components
import Popover from '@/components/ui/Popover.vue'
import PopoverContent from '@/components/ui/PopoverContent.vue'
import PopoverTrigger from '@/components/ui/PopoverTrigger.vue'
import Button from '@/components/ui/Button.vue'
import Switch from '@/components/ui/Switch.vue'

interface Props {
  selectedModel: string | null
}

const props = defineProps<Props>()
const modelStore = useModelStore()

// Available tools configuration
const availableTools = [
  {
    id: 'web_search',
    name: 'Web Search',
    description: 'Search the web for current information',
    icon: Search
  },
  {
    id: 'code_interpreter',
    name: 'Code Interpreter',
    description: 'Execute and analyze code',
    icon: Code
  },
  {
    id: 'vision',
    name: 'Vision',
    description: 'Analyze images and visual content',
    icon: Eye
  },
  {
    id: 'image_generation',
    name: 'Image Generation',
    description: 'Generate images from text descriptions',
    icon: ImageIcon
  },
  {
    id: 'function_calling',
    name: 'Function Calling',
    description: 'Call external functions and APIs',
    icon: Calculator
  }
]

const enabledTools = computed(() => modelStore.enabledTools)
const enabledToolsCount = computed(() => enabledTools.value.length)

const selectedModelData = computed(() => {
  if (!props.selectedModel) return null
  return MODELS_SHARED.find(model => model.id === props.selectedModel)
})

const supportedTools = computed(() => {
  return selectedModelData.value?.abilities || []
})

const hasUnsupportedTools = computed(() => {
  return availableTools.some(tool => !isToolSupported(tool.id))
})

const isToolEnabled = (toolId: string): boolean => {
  return enabledTools.value.includes(toolId)
}

const isToolSupported = (toolId: string): boolean => {
  return supportedTools.value.includes(toolId)
}

const toggleTool = (toolId: string) => {
  if (!isToolSupported(toolId)) return
  
  const currentTools = [...enabledTools.value]
  const index = currentTools.indexOf(toolId)
  
  if (index > -1) {
    currentTools.splice(index, 1)
  } else {
    currentTools.push(toolId)
  }
  
  modelStore.setEnabledTools(currentTools)
}
</script>
