<template>
  <div
    :class="cn(
      'relative flex flex-col',
      isEmpty ? 'h-[calc(100dvh-8px)]' : 'h-[calc(100dvh-64px)]'
    )"
  >
    <!-- Messages area -->
    <Messages
      ref="messagesRef"
      :messages="messages"
      :status="status"
      @retry="handleRetry"
      @edit-and-retry="handleEditAndRetry"
    />

    <!-- Input area with animations -->
    <Transition
      enter-active-class="transition-all duration-200 ease-in-out"
      enter-from-class="opacity-0 translate-y-5"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in-out"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-5"
      mode="out-in"
    >
      <!-- Empty state - centered input -->
      <div
        v-if="isEmpty"
        key="centered-content"
        :class="cn(
          'absolute inset-0 flex flex-col items-center overflow-y-auto [scrollbar-gutter:stable]',
          !isEmpty && 'justify-center'
        )"
      >
        <div
          :class="cn(
            'w-full',
            getChatWidthClass(chatWidth),
            'px-4',
            'flex min-h-[40vh] flex-col justify-end'
          )"
        >
          <div class="mb-6 size-16 rounded-full border-2 opacity-80 mx-auto">
            <Logo />
          </div>
          <div class="mb-8 text-center">
            <h1 class="px-4 font-medium text-3xl text-foreground">
              {{ userName
                ? `What do you want to explore, ${userName.split(' ')[0]}?`
                : 'What do you want to explore?'
              }}
            </h1>
          </div>
          <div class="mt-8 w-full">
            <MultimodalInput
              :status="status"
              @submit="handleInputSubmit"
            />
          </div>
        </div>
      </div>

      <!-- Chat state - bottom input -->
      <div
        v-else
        key="bottom-input"
        class="-bottom-[3.875rem] md:-bottom-10 absolute inset-x-0 z-[10] flex flex-col items-center justify-center gap-2"
      >
        <StickToBottomButton
          :is-at-bottom="isAtBottom"
          @scroll-to-bottom="scrollToBottom"
        />
        <MultimodalInput
          :status="status"
          @submit="handleInputSubmit"
        />
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useChatStore, type UploadedFile } from '@/stores/chat'
import { useModelStore } from '@/stores/model'
import { useChatWidthStore, getChatWidthClass } from '@/stores/chatWidth'
import { cn } from '@/lib/utils'
import { nanoid } from 'nanoid'

interface Props {
  threadId?: string
  folderId?: string
}

defineProps<Props>()

const chatStore = useChatStore()
const modelStore = useModelStore()
const chatWidthStore = useChatWidthStore()

// Refs
const messagesRef = ref()

// Mock data - in real app this would come from API
const messages = ref<Array<{
  id: string
  role: 'user' | 'assistant'
  content: string
  attachments?: UploadedFile[]
}>>([])

const status = ref<'loading' | 'error' | 'idle'>('idle')
const isAtBottom = ref(true)
const userName = ref<string | null>(null)

// Computed
const isEmpty = computed(() => messages.value.length === 0)
const chatWidth = computed(() => chatWidthStore.chatWidth)

// Mock user name - in real app this would come from auth
onMounted(async () => {
  userName.value = 'User' // This would come from authentication
  
  // Initialize model if none selected
  if (!modelStore.selectedModel) {
    const { MODELS_SHARED } = await import('@/stores/model')
    if (MODELS_SHARED.length > 0) {
      modelStore.setSelectedModel(MODELS_SHARED[0].id)
    }
  }
})

// Chat actions
const handleInputSubmit = async (inputValue?: string, fileValues?: UploadedFile[]) => {
  if (!inputValue?.trim() && !fileValues?.length) return

  const userMessage = {
    id: nanoid(),
    role: 'user' as const,
    content: inputValue || '',
    attachments: fileValues
  }

  messages.value.push(userMessage)
  status.value = 'loading'

  // Scroll to bottom after adding message
  await nextTick()
  scrollToBottom()

  // Mock AI response
  setTimeout(() => {
    const assistantMessage = {
      id: nanoid(),
      role: 'assistant' as const,
      content: `I received your message: "${inputValue}". This is a mock response for the migrated chat interface. In a real implementation, this would connect to your AI backend.`
    }
    
    messages.value.push(assistantMessage)
    status.value = 'idle'
    
    // Scroll to bottom after response
    nextTick(() => scrollToBottom())
  }, 1500)
}

const handleRetry = () => {
  if (messages.value.length > 0) {
    const lastUserMessage = [...messages.value].reverse().find(m => m.role === 'user')
    if (lastUserMessage) {
      // Remove last assistant message if it exists
      if (messages.value[messages.value.length - 1]?.role === 'assistant') {
        messages.value.pop()
      }
      handleInputSubmit(lastUserMessage.content, lastUserMessage.attachments)
    }
  }
}

const handleEditAndRetry = (messageId: string) => {
  // In a real app, this would allow editing and retrying from a specific message
  console.log('Edit and retry:', messageId)
}

const scrollToBottom = () => {
  if (messagesRef.value?.scrollRef) {
    const scrollElement = messagesRef.value.scrollRef
    scrollElement.scrollTop = scrollElement.scrollHeight
    isAtBottom.value = true
  }
}

// Mock scroll detection - in real app you'd use intersection observer or scroll events
const checkScrollPosition = () => {
  if (messagesRef.value?.scrollRef) {
    const scrollElement = messagesRef.value.scrollRef
    const isNearBottom = scrollElement.scrollTop + scrollElement.clientHeight >= scrollElement.scrollHeight - 100
    isAtBottom.value = isNearBottom
  }
}

// Handle new chat event
const handleNewChat = () => {
  console.log('[chat] handleNewChat')
  messages.value = []
  status.value = 'idle'
}

onMounted(() => {
  // Set up scroll listener
  if (messagesRef.value?.scrollRef) {
    messagesRef.value.scrollRef.addEventListener('scroll', checkScrollPosition)
  }

  // Listen for new chat events
  if (process.client) {
    document.addEventListener('new_chat', handleNewChat)
  }
})

onUnmounted(() => {
  // Clean up event listener
  if (process.client) {
    document.removeEventListener('new_chat', handleNewChat)
  }
})
</script>
