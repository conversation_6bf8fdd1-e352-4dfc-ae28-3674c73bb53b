<template>
  <Select v-if="modelCapabilities.supportsEffortControl" v-model="reasoningEffort">
    <SelectTrigger class="h-8 w-auto bg-secondary/70 text-xs">
      <div class="flex items-center gap-1.5">
        <Brain class="h-3 w-3" />
        <span class="hidden sm:inline">{{ formatEffortForDisplay(reasoningEffort) }}</span>
      </div>
    </SelectTrigger>
    <SelectContent align="start">
      <div class="p-2">
        <div class="text-sm font-medium mb-2">Reasoning Effort</div>
        <div class="text-xs text-muted-foreground mb-3">
          Control how much the model thinks before responding
        </div>
        <div class="space-y-1">
          <SelectItem
            v-for="effort in availableEfforts"
            :key="effort"
            :value="effort"
            class="flex items-center justify-between"
          >
            <div class="flex items-center gap-2">
              <component :is="getEffortIcon(effort)" class="h-4 w-4" />
              <div>
                <div class="font-medium">{{ formatEffortForDisplay(effort) }}</div>
                <div class="text-xs text-muted-foreground">
                  {{ getEffortDescription(effort) }}
                </div>
              </div>
            </div>
          </SelectItem>
        </div>
      </div>
    </SelectContent>
  </Select>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useModelStore, MODELS_SHARED, type ReasoningEffort } from '@/stores/model'
import { Brain, Zap, Clock, Gauge, Ban } from 'lucide-vue-next'

// Import UI components
import Select from '@/components/ui/Select.vue'
import SelectContent from '@/components/ui/SelectContent.vue'
import SelectItem from '@/components/ui/SelectItem.vue'
import SelectTrigger from '@/components/ui/SelectTrigger.vue'

interface Props {
  selectedModel: string | null
}

const props = defineProps<Props>()
const modelStore = useModelStore()

const reasoningEffort = computed({
  get: () => modelStore.reasoningEffort,
  set: (value: ReasoningEffort) => modelStore.setReasoningEffort(value)
})

const modelCapabilities = computed(() => {
	if (!props.selectedModel) return { supportsEffortControl: false, supportsDisablingReasoning: false }
	const model = MODELS_SHARED.find((m) => m.id === props.selectedModel)
	return {
		supportsEffortControl: model?.abilities.includes("effort_control") ?? false,
		supportsDisablingReasoning: model?.supportsDisablingReasoning ?? false
	}
})


const availableEfforts = computed((): ReasoningEffort[] => {
  const efforts: ReasoningEffort[] = ['low', 'medium', 'high']
  if (modelCapabilities.value.supportsDisablingReasoning) {
    efforts.unshift('off' as ReasoningEffort)
  }
  return efforts
})

const formatEffortForDisplay = (effort: ReasoningEffort): string => {
  return effort.charAt(0).toUpperCase() + effort.slice(1)
}

const getEffortDescription = (effort: ReasoningEffort): string => {
  switch (effort) {
    case 'off':
      return 'No reasoning, fastest responses'
    case 'low':
      return 'Quick thinking, faster responses'
    case 'medium':
      return 'Balanced thinking and speed'
    case 'high':
      return 'Deep thinking, slower responses'
    default:
      return ''
  }
}

const getEffortIcon = (effort: ReasoningEffort) => {
  switch (effort) {
    case 'off':
      return Ban
    case 'low':
      return Zap
    case 'medium':
      return Clock
    case 'high':
      return Gauge
    default:
      return Brain
  }
}
</script>
