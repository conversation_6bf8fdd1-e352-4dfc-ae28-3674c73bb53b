<template>
  <header class="pointer-events-none absolute top-0 z-50 w-full">
    <div class="flex w-full items-center justify-between">
      <!-- Left side - Sidebar trigger -->
      <div class="pointer-events-auto">
        <div class="flex items-center gap-2 rounded-xl bg-background/10 p-2 backdrop-blur-sm">
          <SidebarTrigger />
          <div class="h-4 w-px bg-border" />
        </div>
      </div>

      <!-- Right side - Actions -->
      <div class="pointer-events-auto flex items-center space-x-2 rounded-xl bg-background/10 p-2 backdrop-blur-sm">
        <!-- Share button (if thread exists) -->
        <Button
          v-if="threadId"
          variant="ghost"
          size="icon"
          class="h-7 w-7"
          @click="handleShare"
        >
          <Share class="h-4 w-4" />
          <span class="sr-only">Share</span>
        </Button>

        <!-- Theme switcher -->
        <ThemeSwitcher />

        <div class="h-4 w-px bg-border" />

        <!-- User dropdown -->
        <UserDropdown />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { Share } from 'lucide-vue-next'

// Import components
import SidebarTrigger from '@/components/ui/SidebarTrigger.vue'
import Button from '@/components/ui/Button.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import UserDropdown from '@/components/UserDropdown.vue'

interface Props {
  threadId?: string
}

defineProps<Props>()

const handleShare = () => {
  // In real app: open share modal
  console.log('Share thread')
}
</script>
