<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-md">
      <DialogHeader>
        <DialogTitle>Create New Folder</DialogTitle>
        <div class="text-sm text-muted-foreground">
          Folders are a great way to organize your threads
        </div>
      </DialogHeader>
      
      <div class="space-y-6">
        <!-- Folder Name -->
        <div class="space-y-2">
          <Label for="folder-name">Name</Label>
          <Input
            id="folder-name"
            v-model="folderName"
            placeholder="Enter folder name"
            class="max-w-[50%]"
            :disabled="isCreating"
            @keydown.enter="handleCreate"
            ref="nameInputRef"
          />
        </div>
        
        <!-- Folder Description -->
        <div class="space-y-2">
          <Label for="folder-description">Description (optional)</Label>
          <Textarea
            id="folder-description"
            v-model="folderDescription"
            placeholder="Enter folder description"
            :disabled="isCreating"
            rows="3"
          />
        </div>
        
        <!-- Color Selection -->
        <div class="space-y-2">
          <Label>Color</Label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="color in PROJECT_COLORS"
              :key="color"
              type="button"
              @click="folderColor = color"
              :class="cn(
                'h-8 w-8 rounded-full border-2 transition-all',
                folderColor === color 
                  ? 'border-foreground scale-110' 
                  : 'border-transparent hover:scale-105'
              )"
              :style="{ backgroundColor: getColorValue(color) }"
              :disabled="isCreating"
            >
              <Check 
                v-if="folderColor === color" 
                class="h-4 w-4 text-white mx-auto" 
              />
            </button>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end gap-2 pt-4">
        <Button
          variant="outline"
          @click="$emit('update:open', false)"
          :disabled="isCreating"
        >
          Cancel
        </Button>
        <Button
          @click="handleCreate"
          :disabled="isCreating || !folderName.trim()"
        >
          <Loader2 v-if="isCreating" class="h-4 w-4 animate-spin mr-2" />
          {{ isCreating ? 'Creating...' : 'Create' }}
        </Button>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { Check, Loader2 } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

// Import UI components
import Dialog from '@/components/ui/Dialog.vue'
import DialogContent from '@/components/ui/DialogContent.vue'
import DialogHeader from '@/components/ui/DialogHeader.vue'
import DialogTitle from '@/components/ui/DialogTitle.vue'
import Button from '@/components/ui/Button.vue'
import Input from '@/components/ui/Input.vue'
import Label from '@/components/ui/Label.vue'
import Textarea from '@/components/ui/Textarea.vue'

interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'created', folder: { name: string; description?: string; color: string }): void
}

const emit = defineEmits<Emits>()

// Form state
const folderName = ref('')
const folderDescription = ref('')
const folderColor = ref('blue')
const isCreating = ref(false)
const nameInputRef = ref<HTMLInputElement>()

// Project colors (matching the React version)
const PROJECT_COLORS = [
  'blue', 'green', 'yellow', 'red', 'purple', 'pink', 'indigo', 'gray'
]

const getColorValue = (color: string): string => {
  const colorMap: Record<string, string> = {
    blue: '#3b82f6',
    green: '#10b981',
    yellow: '#f59e0b',
    red: '#ef4444',
    purple: '#8b5cf6',
    pink: '#ec4899',
    indigo: '#6366f1',
    gray: '#6b7280'
  }
  return colorMap[color] || colorMap.blue
}

const handleCreate = async () => {
  const trimmedName = folderName.value.trim()
  if (!trimmedName) {
    // In a real app, you'd show a toast error
    console.error('Folder name cannot be empty')
    return
  }

  isCreating.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newFolder = {
      name: trimmedName,
      description: folderDescription.value.trim() || undefined,
      color: folderColor.value
    }
    
    emit('created', newFolder)
    
    // Reset form
    folderName.value = ''
    folderDescription.value = ''
    folderColor.value = 'blue'
    
    // Close dialog
    emit('update:open', false)
    
    // In a real app, you'd show a success toast
    console.log('Folder created successfully')
  } catch (error) {
    console.error('Failed to create folder:', error)
    // In a real app, you'd show an error toast
  } finally {
    isCreating.value = false
  }
}

// Get props
const props = defineProps<Props>()

// Focus name input when dialog opens
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      nameInputRef.value?.focus()
    })
  }
}, { immediate: true })
</script>
