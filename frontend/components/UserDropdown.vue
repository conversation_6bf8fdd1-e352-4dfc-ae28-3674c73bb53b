<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button
        variant="ghost"
        size="icon"
        class="h-7 w-7 rounded-full"
      >
        <div class="h-5 w-5 rounded-full bg-primary flex items-center justify-center">
          <User class="h-3 w-3 text-primary-foreground" />
        </div>
        <span class="sr-only">User menu</span>
      </Button>
    </DropdownMenuTrigger>
    
    <DropdownMenuContent class="w-56" align="end">
      <DropdownMenuLabel class="font-normal">
        <div class="flex flex-col space-y-1">
          <p class="font-medium text-sm leading-none">
            {{ userName || 'User' }}
          </p>
          <p class="text-muted-foreground text-xs leading-none">
            {{ userEmail || '<EMAIL>' }}
          </p>
        </div>
      </DropdownMenuLabel>
      
      <DropdownMenuSeparator />
      
      <DropdownMenuItem @click="handleSettings">
        <Settings class="h-4 w-4" />
        <span>Settings</span>
      </DropdownMenuItem>
      
      <DropdownMenuItem as-child>
        <a href="https://docs.intern3.chat" target="_blank" rel="noopener noreferrer">
          <BookText class="h-4 w-4" />
          <span>Docs</span>
        </a>
      </DropdownMenuItem>
      
      <DropdownMenuSeparator />
      
      <DropdownMenuItem as-child>
        <a href="https://github.com/intern3-chat/intern3-chat" target="_blank" rel="noopener noreferrer">
          <Github class="h-4 w-4" />
          <span>GitHub</span>
        </a>
      </DropdownMenuItem>
      
      <DropdownMenuItem as-child>
        <a href="https://x.com/intern3chat" target="_blank" rel="noopener noreferrer">
          <Twitter class="h-4 w-4" />
          <span>Twitter</span>
        </a>
      </DropdownMenuItem>
      
      <DropdownMenuSeparator />
      
      <DropdownMenuItem @click="handleSignOut">
        <LogOut class="h-4 w-4" />
        <span>Sign Out</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>

  <!-- Settings Dialog -->
  <SettingsDialog
    v-model:open="showSettingsDialog"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  User, 
  Settings, 
  BookText, 
  Github, 
  Twitter, 
  LogOut 
} from 'lucide-vue-next'

// Import UI components
import Button from '@/components/ui/Button.vue'
import DropdownMenu from '@/components/ui/DropdownMenu.vue'
import DropdownMenuContent from '@/components/ui/DropdownMenuContent.vue'
import DropdownMenuItem from '@/components/ui/DropdownMenuItem.vue'
import DropdownMenuLabel from '@/components/ui/DropdownMenuLabel.vue'
import DropdownMenuSeparator from '@/components/ui/DropdownMenuSeparator.vue'
import DropdownMenuTrigger from '@/components/ui/DropdownMenuTrigger.vue'
import SettingsDialog from '@/components/SettingsDialog.vue'

// Mock user data - in real app this would come from auth
const userName = ref('User')
const userEmail = ref('<EMAIL>')
const showSettingsDialog = ref(false)

const handleSettings = () => {
  showSettingsDialog.value = true
}

const handleSignOut = () => {
  // In real app: handle sign out
  console.log('Sign out')
}
</script>
