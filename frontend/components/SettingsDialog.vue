<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[80vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle>Settings</DialogTitle>
        <div class="text-sm text-muted-foreground">
          Manage your account settings and preferences
        </div>
      </DialogHeader>
      
      <div class="flex gap-6 overflow-hidden">
        <!-- Navigation -->
        <div class="w-48 flex-shrink-0">
          <nav class="space-y-1">
            <button
              v-for="item in settingsNavItems"
              :key="item.id"
              @click="activeTab = item.id"
              :class="cn(
                'flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                activeTab === item.id
                  ? 'bg-muted text-foreground'
                  : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground'
              )"
            >
              <component :is="item.icon" class="h-4 w-4" />
              {{ item.title }}
            </button>
          </nav>
        </div>
        
        <!-- Content -->
        <div class="flex-1 overflow-y-auto">
          <div v-if="activeTab === 'profile'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium">Profile</h3>
              <p class="text-sm text-muted-foreground">
                Manage your profile information
              </p>
            </div>
            
            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="name">Name</Label>
                <Input
                  id="name"
                  v-model="profileSettings.name"
                  placeholder="Your name"
                />
              </div>
              
              <div class="space-y-2">
                <Label for="email">Email</Label>
                <Input
                  id="email"
                  v-model="profileSettings.email"
                  type="email"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>
          
          <div v-else-if="activeTab === 'models'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium">Models</h3>
              <p class="text-sm text-muted-foreground">
                Configure AI model preferences
              </p>
            </div>
            
            <div class="space-y-4">
              <div class="space-y-2">
                <Label>Default Model</Label>
                <ModelSelector />
              </div>
              
              <div class="space-y-2">
                <Label>Default Reasoning Effort</Label>
                <ReasoningEffortSelector :selected-model="selectedModel" />
              </div>
            </div>
          </div>
          
          <div v-else-if="activeTab === 'appearance'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium">Appearance</h3>
              <p class="text-sm text-muted-foreground">
                Customize the look and feel
              </p>
            </div>
            
            <div class="space-y-4">
              <div class="space-y-2">
                <Label>Theme</Label>
                <ThemeSwitcher />
              </div>
              
              <div class="space-y-2">
                <Label>Chat Width</Label>
                <Select v-model="chatWidth">
                  <SelectTrigger class="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="narrow">Narrow</SelectItem>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="wide">Wide</SelectItem>
                    <SelectItem value="full">Full Width</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <div v-else class="space-y-6">
            <div>
              <h3 class="text-lg font-medium">{{ getCurrentTabTitle() }}</h3>
              <p class="text-sm text-muted-foreground">
                Settings for {{ getCurrentTabTitle().toLowerCase() }}
              </p>
            </div>
            
            <div class="text-center py-8 text-muted-foreground">
              <p>Settings for this section are coming soon.</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end gap-2 pt-4 border-t">
        <Button
          variant="outline"
          @click="$emit('update:open', false)"
        >
          Cancel
        </Button>
        <Button @click="handleSave">
          Save Changes
        </Button>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useModelStore } from '@/stores/model'
import { useChatWidthStore } from '@/stores/chatWidth'
import { cn } from '@/lib/utils'
import {
  User,
  Bot,
  Palette,
  Key,
  Settings as SettingsIcon,
  BarChart3,
  Paperclip
} from 'lucide-vue-next'

// Import UI components
import Dialog from '@/components/ui/Dialog.vue'
import DialogContent from '@/components/ui/DialogContent.vue'
import DialogHeader from '@/components/ui/DialogHeader.vue'
import DialogTitle from '@/components/ui/DialogTitle.vue'
import Button from '@/components/ui/Button.vue'
import Input from '@/components/ui/Input.vue'
import Label from '@/components/ui/Label.vue'
import Select from '@/components/ui/Select.vue'
import SelectContent from '@/components/ui/SelectContent.vue'
import SelectItem from '@/components/ui/SelectItem.vue'
import SelectTrigger from '@/components/ui/SelectTrigger.vue'
import SelectValue from '@/components/ui/SelectValue.vue'
import ModelSelector from '@/components/ModelSelector.vue'
import ReasoningEffortSelector from '@/components/ReasoningEffortSelector.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'

interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
}

defineProps<Props>()
defineEmits<Emits>()

const modelStore = useModelStore()
const chatWidthStore = useChatWidthStore()

const activeTab = ref('profile')
const selectedModel = computed(() => modelStore.selectedModel)
const chatWidth = computed({
  get: () => chatWidthStore.chatWidth,
  set: (value) => chatWidthStore.setChatWidth(value)
})

// Profile settings
const profileSettings = ref({
  name: 'User',
  email: '<EMAIL>'
})

const settingsNavItems = [
  { id: 'profile', title: 'Profile', icon: User },
  { id: 'models', title: 'Models', icon: Bot },
  { id: 'appearance', title: 'Appearance', icon: Palette },
  { id: 'providers', title: 'Providers', icon: Key },
  { id: 'usage', title: 'Usage', icon: BarChart3 },
  { id: 'attachments', title: 'Attachments', icon: Paperclip }
]

const getCurrentTabTitle = () => {
  const item = settingsNavItems.find(item => item.id === activeTab.value)
  return item?.title || 'Settings'
}

const handleSave = () => {
  // In real app: save settings to API/store
  console.log('Settings saved:', {
    profile: profileSettings.value,
    chatWidth: chatWidth.value
  })
}
</script>
